import React, { SVGProps } from 'react';

const SvgThermostatAuto = (props: SVGProps<SVGSVGElement>) => {
	return (
		<svg viewBox='0 0 24 24' fill='currentColor' className='svg-icon' {...props}>
			<path fill='none' d='M0 0h24v24H0z' />
			<path
				d='M9.8 13.6L9 13V6c0-.55-.45-1-1-1s-1 .45-1 1v7l-.8.6C5.45 14.16 5 15.06 5 16h6c0-.94-.45-1.83-1.2-2.4z'
				opacity={0.3}
			/>
			<path d='M11 12V6c0-1.66-1.34-3-3-3S5 4.34 5 6v6c-1.21.91-2 2.37-2 4 0 1.12.38 2.14 1 2.97V19h.02c.91 1.21 2.35 2 3.98 2s3.06-.79 3.98-2H12v-.03c.62-.83 1-1.85 1-2.97 0-1.63-.79-3.09-2-4zm-6 4c0-.94.45-1.84 1.2-2.4L7 13V6c0-.55.45-1 1-1s1 .45 1 1v7l.8.6c.75.57 1.2 1.46 1.2 2.4H5zM18.62 4h-1.61l-3.38 9h1.56l.81-2.3h3.63l.8 2.3H22l-3.38-9zm-2.15 5.39l1.31-3.72h.08l1.31 3.72h-2.7z' />
		</svg>
	);
};

export default SvgThermostatAuto;
